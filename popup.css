/* Popup styles for Read Aloud Pal settings panel */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 320px;
  min-height: 400px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #333;
  line-height: 1.5;
}

.container {
  padding: 16px;
}

.header {
  text-align: center;
  margin-bottom: 20px;
  padding: 20px 16px 16px;
  border-bottom: 2px solid #e9ecef;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px 8px 0 0;
  backdrop-filter: blur(10px);
}

.logo {
  width: 40px;
  height: 40px;
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
  transition: transform 0.2s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.header h1 {
  font-size: 20px;
  font-weight: 700;
  color: #4a90e2;
  margin-bottom: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 12px;
  color: #6c757d;
}

.settings {
  margin-bottom: 20px;
  padding: 0 4px;
}

.setting-group {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

.setting-group:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.setting-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #495057;
}

.setting-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.setting-input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.repeat-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e9ecef;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4a90e2;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.number-input {
  width: 50px;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.repeat-description {
  margin-top: 6px;
  font-size: 11px;
  color: #6c757d;
  font-style: italic;
}

#repeat-display {
  font-weight: 600;
  color: #4a90e2;
}

/* Toggle switch styles */
.toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}

.toggle-label input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background: #ccc;
  border-radius: 12px;
  margin-right: 12px;
  transition: background 0.3s ease;
}

.toggle-slider::before {
  content: "";
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
  background: #4a90e2;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-text {
  flex: 1;
}

.toggle-description {
  margin-top: 6px;
  font-size: 11px;
  color: #6c757d;
  font-style: italic;
}

.footer {
  text-align: center;
  padding: 16px;
  margin-top: 20px;
  border-top: 2px solid #e9ecef;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 0 0 8px 8px;
  backdrop-filter: blur(10px);
}

.version {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 6px;
}

.description {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* Error and success messages */
.error-message,
.success-message {
  display: none;
  padding: 8px 12px;
  margin: 12px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
