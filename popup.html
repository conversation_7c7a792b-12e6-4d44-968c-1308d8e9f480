<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Read Aloud Pal Settings</title>
    <link rel="stylesheet" href="popup.css" />
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img src="icons/icon32.png" alt="Read Aloud Pal" class="logo" />
        <h1>朗讀小夥伴</h1>
        <p class="subtitle">Read Aloud Pal</p>
      </div>

      <div class="settings">
        <div class="setting-group">
          <label for="language-select">語言 / Language:</label>
          <select id="language-select" class="setting-input">
            <option value="zh-CN">中文 (Mandarin)</option>
            <option value="en-US">English</option>
          </select>
        </div>

        <div class="setting-group">
          <label for="repeat-count"
            >重複次數 / Repeat Count: <span id="repeat-display">1</span></label
          >
          <div class="repeat-control">
            <input
              type="range"
              id="repeat-slider"
              min="1"
              max="10"
              value="1"
              class="slider"
            />
            <input
              type="number"
              id="repeat-input"
              min="1"
              max="10"
              value="1"
              class="number-input"
            />
          </div>
          <div class="repeat-description">
            <span id="repeat-text">Text will be read once</span>
          </div>
        </div>

        <div class="setting-group">
          <label class="toggle-label">
            <input type="checkbox" id="floating-button-toggle" checked />
            <span class="toggle-slider"></span>
            <span class="toggle-text">顯示浮動按鈕 / Show Floating Button</span>
          </label>
          <div class="toggle-description">
            <span id="toggle-status"
              >Floating button will appear when text is selected</span
            >
          </div>
        </div>
      </div>

      <div class="footer">
        <p class="version">Version 1.0.0</p>
        <p class="description">
          Select text on any webpage and click the floating button or
          right-click to read aloud!
        </p>
      </div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
