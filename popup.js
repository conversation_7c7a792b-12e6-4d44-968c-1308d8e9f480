// Popup script for Read Aloud Pal settings panel

class SettingsPanel {
  constructor() {
    this.languageSelect = document.getElementById('language-select');
    this.repeatSlider = document.getElementById('repeat-slider');
    this.repeatInput = document.getElementById('repeat-input');
    this.floatingButtonToggle = document.getElementById('floating-button-toggle');
    
    this.init();
  }
  
  async init() {
    // Load current settings
    await this.loadSettings();
    
    // Add event listeners
    this.languageSelect.addEventListener('change', this.saveSettings.bind(this));
    this.repeatSlider.addEventListener('input', this.handleRepeatChange.bind(this));
    this.repeatInput.addEventListener('input', this.handleRepeatInputChange.bind(this));
    this.floatingButtonToggle.addEventListener('change', this.saveSettings.bind(this));
    
    console.log('Settings panel initialized');
  }
  
  async loadSettings() {
    try {
      const settings = await chrome.storage.sync.get([
        'language',
        'repeatCount', 
        'showFloatingButton'
      ]);
      
      // Set language
      this.languageSelect.value = settings.language || 'zh-CN';
      
      // Set repeat count
      const repeatCount = settings.repeatCount || 1;
      this.repeatSlider.value = repeatCount;
      this.repeatInput.value = repeatCount;
      
      // Set floating button toggle
      this.floatingButtonToggle.checked = settings.showFloatingButton !== false;
      
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }
  
  async saveSettings() {
    try {
      const settings = {
        language: this.languageSelect.value,
        repeatCount: parseInt(this.repeatSlider.value),
        showFloatingButton: this.floatingButtonToggle.checked
      };
      
      await chrome.storage.sync.set(settings);
      
      // Notify content scripts about settings change
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { 
          action: 'settingsChanged', 
          settings: settings 
        }).catch(() => {
          // Ignore errors if content script is not ready
        });
      }
      
      console.log('Settings saved:', settings);
      
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }
  
  handleRepeatChange() {
    // Sync slider with input
    this.repeatInput.value = this.repeatSlider.value;
    this.saveSettings();
  }
  
  handleRepeatInputChange() {
    // Validate and sync input with slider
    let value = parseInt(this.repeatInput.value);
    
    if (isNaN(value) || value < 1) {
      value = 1;
    } else if (value > 10) {
      value = 10;
    }
    
    this.repeatInput.value = value;
    this.repeatSlider.value = value;
    this.saveSettings();
  }
}

// Initialize settings panel when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new SettingsPanel();
});
