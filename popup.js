// Popup script for Read Aloud Pal settings panel

class SettingsPanel {
  constructor() {
    this.languageSelect = document.getElementById("language-select");
    this.repeatSlider = document.getElementById("repeat-slider");
    this.repeatInput = document.getElementById("repeat-input");
    this.repeatDisplay = document.getElementById("repeat-display");
    this.repeatText = document.getElementById("repeat-text");
    this.floatingButtonToggle = document.getElementById(
      "floating-button-toggle"
    );
    this.toggleStatus = document.getElementById("toggle-status");
    this.isLoading = false;
    this.saveTimeout = null;

    this.init();
  }

  async init() {
    try {
      // Show loading state
      this.setLoadingState(true);

      // Load current settings
      await this.loadSettings();

      // Add event listeners
      this.languageSelect.addEventListener(
        "change",
        this.handleLanguageChange.bind(this)
      );
      this.repeatSlider.addEventListener(
        "input",
        this.handleRepeatChange.bind(this)
      );
      this.repeatInput.addEventListener(
        "input",
        this.handleRepeatInputChange.bind(this)
      );
      this.floatingButtonToggle.addEventListener(
        "change",
        this.handleToggleChange.bind(this)
      );

      // Add keyboard shortcuts
      document.addEventListener("keydown", this.handleKeydown.bind(this));

      console.log("Settings panel initialized");

      // Hide loading state
      this.setLoadingState(false);
    } catch (error) {
      console.error("Error initializing settings panel:", error);
      this.showError("Failed to initialize settings panel");
      this.setLoadingState(false);
    }
  }

  // Set loading state for UI feedback
  setLoadingState(loading) {
    this.isLoading = loading;
    const elements = [
      this.languageSelect,
      this.repeatSlider,
      this.repeatInput,
      this.floatingButtonToggle,
    ];
    elements.forEach((element) => {
      if (element) {
        element.disabled = loading;
      }
    });
  }

  // Handle keyboard shortcuts
  handleKeydown(event) {
    if (event.key === "Escape") {
      window.close();
    }
  }

  async loadSettings() {
    try {
      const settings = await chrome.storage.sync.get([
        "language",
        "repeatCount",
        "showFloatingButton",
      ]);

      // Set language with validation
      const language = settings.language || "zh-CN";
      if (this.isValidLanguage(language)) {
        this.languageSelect.value = language;
      } else {
        console.warn("Invalid language setting, using default");
        this.languageSelect.value = "zh-CN";
      }

      // Set repeat count with validation
      const repeatCount = Math.max(1, Math.min(10, settings.repeatCount || 1));
      this.repeatSlider.value = repeatCount;
      this.repeatInput.value = repeatCount;
      this.updateRepeatDisplay(repeatCount);

      // Set floating button toggle
      const showFloatingButton = settings.showFloatingButton !== false;
      this.floatingButtonToggle.checked = showFloatingButton;
      this.updateToggleStatus(showFloatingButton);

      console.log("Settings loaded:", {
        language,
        repeatCount,
        showFloatingButton: settings.showFloatingButton,
      });
    } catch (error) {
      console.error("Error loading settings:", error);
      this.showError("Failed to load settings");
    }
  }

  // Validate language selection
  isValidLanguage(language) {
    const validLanguages = ["zh-CN", "en-US"];
    return validLanguages.includes(language);
  }

  // Show error message to user
  showError(message) {
    // Create or update error element
    let errorElement = document.querySelector(".error-message");
    if (!errorElement) {
      errorElement = document.createElement("div");
      errorElement.className = "error-message";
      document.querySelector(".container").appendChild(errorElement);
    }

    errorElement.textContent = message;
    errorElement.style.display = "block";

    // Hide error after 3 seconds
    setTimeout(() => {
      if (errorElement) {
        errorElement.style.display = "none";
      }
    }, 3000);
  }

  async saveSettings() {
    try {
      const settings = {
        language: this.languageSelect.value,
        repeatCount: parseInt(this.repeatSlider.value),
        showFloatingButton: this.floatingButtonToggle.checked,
      };

      await chrome.storage.sync.set(settings);

      // Notify content scripts about settings change
      const tabs = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      if (tabs[0]) {
        chrome.tabs
          .sendMessage(tabs[0].id, {
            action: "settingsChanged",
            settings: settings,
          })
          .catch(() => {
            // Ignore errors if content script is not ready
          });
      }

      console.log("Settings saved:", settings);
    } catch (error) {
      console.error("Error saving settings:", error);
    }
  }

  // Handle language change
  handleLanguageChange() {
    this.saveSettings();
  }

  // Handle toggle change
  handleToggleChange() {
    const isEnabled = this.floatingButtonToggle.checked;
    this.updateToggleStatus(isEnabled);
    this.saveSettings();
  }

  // Update toggle status display
  updateToggleStatus(enabled) {
    if (this.toggleStatus) {
      if (enabled) {
        this.toggleStatus.textContent =
          "Floating button will appear when text is selected";
        this.toggleStatus.style.color = "#28a745";
      } else {
        this.toggleStatus.textContent =
          "Only right-click context menu will be available";
        this.toggleStatus.style.color = "#6c757d";
      }
    }
  }

  // Update repeat count display
  updateRepeatDisplay(count) {
    if (this.repeatDisplay) {
      this.repeatDisplay.textContent = count;
    }

    if (this.repeatText) {
      if (count === 1) {
        this.repeatText.textContent = "Text will be read once";
      } else {
        this.repeatText.textContent = `Text will be read ${count} times`;
      }
    }
  }

  handleRepeatChange() {
    // Sync slider with input
    const value = parseInt(this.repeatSlider.value);
    this.repeatInput.value = value;
    this.updateRepeatDisplay(value);
    this.saveSettings();
  }

  handleRepeatInputChange() {
    // Validate and sync input with slider
    let value = parseInt(this.repeatInput.value);

    if (isNaN(value) || value < 1) {
      value = 1;
    } else if (value > 10) {
      value = 10;
    }

    this.repeatInput.value = value;
    this.repeatSlider.value = value;
    this.updateRepeatDisplay(value);
    this.saveSettings();
  }
}

// Initialize settings panel when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  new SettingsPanel();
});
