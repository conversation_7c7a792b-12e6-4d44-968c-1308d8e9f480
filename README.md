# Read Aloud Pal (TTS 朗讀小夥伴)

A Chrome extension designed for children and language learners to read selected text aloud with high-quality TTS voices.

## Features

- **Text Selection & TTS**: Select any text on a webpage and click the floating button to hear it read aloud
- **Right-Click Context Menu**: Alternative way to trigger TTS through right-click menu
- **Language Support**: Chinese (Mandarin) and English TTS voices
- **Repeat Functionality**: Set text to be repeated 1-10 times for better learning
- **Customizable Settings**: Toggle floating button, adjust repeat count, and change language
- **Child-Friendly UI**: Simple, intuitive interface designed for children and parents

## Installation

### For Development

1. Clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the project directory
5. The extension should now appear in your Chrome toolbar

### For Production

The extension will be available on the Chrome Web Store once published.

## Usage

1. **Basic Usage**: 
   - Select any text on a webpage
   - Click the floating play button that appears
   - The text will be read aloud using TTS

2. **Right-Click Method**:
   - Select text and right-click
   - Choose "使用朗讀小夥伴唸出 (Read with Read Aloud Pal)"

3. **Settings**:
   - Click the extension icon in Chrome toolbar
   - Adjust language (Chinese/English)
   - Set repeat count (1-10 times)
   - Toggle floating button on/off

## Project Structure

```
├── manifest.json          # Extension manifest
├── background.js          # Background service worker
├── content.js            # Content script for text selection
├── content.css           # Styles for floating button
├── popup.html            # Settings panel HTML
├── popup.css             # Settings panel styles
├── popup.js              # Settings panel functionality
├── icons/                # Extension icons
└── README.md             # This file
```

## Technical Details

- **Manifest Version**: 3 (latest Chrome extension standard)
- **Permissions**: activeTab, contextMenus, storage, tts
- **TTS Engine**: Chrome's built-in TTS API
- **Storage**: Chrome sync storage for cross-device settings

## Development Status

This project is currently in development. See the task list for implementation progress.

## License

This project is developed as part of a Chrome extension development exercise.
