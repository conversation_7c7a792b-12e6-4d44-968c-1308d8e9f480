/* Styles for Read Aloud Pal floating button */

.read-aloud-pal-button {
  width: 28px;
  height: 28px;
  background: rgba(74, 144, 226, 0.95);
  border: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  font-weight: bold;
}

.read-aloud-pal-button:hover {
  transform: scale(1.15);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.35);
  border-color: rgba(255, 255, 255, 1);
}

.read-aloud-pal-button:active {
  transform: scale(0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.read-aloud-pal-button:focus {
  outline: 3px solid rgba(74, 144, 226, 0.5);
  outline-offset: 2px;
}

/* Loading state */
.read-aloud-pal-button[data-loading="true"] {
  cursor: wait;
  animation: pulse 1.5s ease-in-out infinite;
}

/* Playing state */
.read-aloud-pal-button[data-playing="true"] {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Animations */
@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

@keyframes glow {
  from {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  to {
    box-shadow: 0 4px 16px rgba(220, 53, 69, 0.4);
  }
}

/* Ensure the button doesn't interfere with page content */
.read-aloud-pal-button {
  pointer-events: auto;
  z-index: 2147483647; /* Maximum z-index value */
}
