// Background script for Read Aloud Pal Chrome Extension
// Handles TTS functionality and context menu creation

// Global state management for TTS queue
let currentSpeechState = {
  isPlaying: false,
  currentTabId: null,
  currentText: "",
  currentRepeatCount: 0,
  totalRepeats: 1,
  requestId: null, // Unique identifier for current request
  startTime: null, // When current speech started
  timeoutId: null, // Timeout for detecting TTS failures
};

// Queue management
let speechQueue = [];
let isProcessingQueue = false;

// Initialize extension when installed
chrome.runtime.onInstalled.addListener(async () => {
  console.log("Read Aloud Pal extension installed");

  try {
    // Check if settings already exist
    const existingSettings = await chrome.storage.sync.get([
      "language",
      "repeatCount",
      "showFloatingButton",
    ]);

    // Set default settings only if they don't exist
    const defaultSettings = {
      language: existingSettings.language || "zh-CN", // Default to Chinese
      repeatCount: existingSettings.repeatCount || 1,
      showFloatingButton: existingSettings.showFloatingButton !== false,
    };

    await chrome.storage.sync.set(defaultSettings);
    console.log("Settings initialized:", defaultSettings);
  } catch (error) {
    console.error("Error initializing settings:", error);
    // Fallback to basic settings
    chrome.storage.sync.set({
      language: "zh-CN",
      repeatCount: 1,
      showFloatingButton: true,
    });
  }

  // Create context menu item
  createContextMenu();

  // Initialize TTS voices
  initializeTTSVoices();

  // Update context menu title based on default language
  setTimeout(updateContextMenuTitle, 100);
});

// Listen for storage changes to update context menu and validate settings
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === "sync") {
    if (changes.language) {
      updateContextMenuTitle();
    }

    // Validate and fix any invalid settings
    validateAndFixSettings(changes);
  }
});

// Validate and fix invalid settings
async function validateAndFixSettings(changes) {
  try {
    const fixes = {};
    let needsFix = false;

    // Validate language
    if (
      changes.language &&
      !["zh-CN", "en-US"].includes(changes.language.newValue)
    ) {
      fixes.language = "zh-CN";
      needsFix = true;
      console.warn("Invalid language setting detected, fixing to default");
    }

    // Validate repeat count
    if (changes.repeatCount) {
      const count = changes.repeatCount.newValue;
      if (typeof count !== "number" || count < 1 || count > 10) {
        fixes.repeatCount = 1;
        needsFix = true;
        console.warn("Invalid repeat count detected, fixing to default");
      }
    }

    // Validate floating button setting
    if (
      changes.showFloatingButton &&
      typeof changes.showFloatingButton.newValue !== "boolean"
    ) {
      fixes.showFloatingButton = true;
      needsFix = true;
      console.warn(
        "Invalid floating button setting detected, fixing to default"
      );
    }

    // Apply fixes if needed
    if (needsFix) {
      await chrome.storage.sync.set(fixes);
      console.log("Settings fixed:", fixes);
    }
  } catch (error) {
    console.error("Error validating settings:", error);
  }
}

// Create context menu with proper error handling
function createContextMenu() {
  try {
    // Remove existing context menu items first
    chrome.contextMenus.removeAll(() => {
      // Create the main context menu item
      chrome.contextMenus.create(
        {
          id: "readAloudPal",
          title: "使用朗讀小夥伴唸出 (Read with Read Aloud Pal)",
          contexts: ["selection"],
        },
        () => {
          if (chrome.runtime.lastError) {
            console.error(
              "Error creating context menu:",
              chrome.runtime.lastError
            );
          } else {
            console.log("Context menu created successfully");
          }
        }
      );
    });
  } catch (error) {
    console.error("Failed to create context menu:", error);
  }
}

// Initialize and cache available TTS voices
function initializeTTSVoices() {
  chrome.tts.getVoices((voices) => {
    console.log(`Available TTS voices: ${voices.length}`);

    if (voices.length === 0) {
      console.warn("No TTS voices available. TTS functionality may not work.");
    } else {
      // Log available voices for debugging
      voices.forEach((voice, index) => {
        console.log(
          `Voice ${index}: ${voice.voiceName || "Default"} (${
            voice.lang || "Unknown"
          }) - ${voice.remote ? "Remote" : "Local"}`
        );
      });
    }

    // Cache voices for better performance
    chrome.storage.local.set({ availableVoices: voices });
  });
}

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "readAloudPal") {
    handleContextMenuClick(info, tab);
  }
});

// Handle context menu click with validation
function handleContextMenuClick(info, tab) {
  try {
    // Validate selection text
    if (!info.selectionText || info.selectionText.trim().length === 0) {
      console.warn("No text selected for context menu TTS");
      return;
    }

    // Validate tab
    if (!tab || !tab.id) {
      console.error("Invalid tab for context menu TTS");
      return;
    }

    // Check text length limits
    const selectedText = info.selectionText.trim();
    if (selectedText.length > 5000) {
      console.warn("Selected text too long for TTS, truncating");
    }

    // Log context menu usage
    console.log(
      "Context menu TTS triggered for text:",
      selectedText.substring(0, 50) + (selectedText.length > 50 ? "..." : "")
    );

    // Handle the TTS request
    handleTTSRequest(selectedText, tab.id);
  } catch (error) {
    console.error("Error handling context menu click:", error);
  }
}

// Update context menu title based on current language
async function updateContextMenuTitle() {
  try {
    const settings = await chrome.storage.sync.get(["language"]);
    const language = settings.language || "zh-CN";

    let title;
    if (language.startsWith("zh")) {
      title = "使用朗讀小夥伴唸出 (Read with Read Aloud Pal)";
    } else {
      title = "Read with Read Aloud Pal (使用朗讀小夥伴唸出)";
    }

    chrome.contextMenus.update("readAloudPal", { title: title }, () => {
      if (chrome.runtime.lastError) {
        console.error(
          "Error updating context menu title:",
          chrome.runtime.lastError
        );
      }
    });
  } catch (error) {
    console.error("Error updating context menu title:", error);
  }
}

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "speak") {
    const requestId = generateRequestId();
    handleTTSRequest(request.text, sender.tab.id, requestId);
    sendResponse({ success: true, requestId: requestId });
  } else if (request.action === "stop") {
    stopCurrentSpeech();
    sendResponse({ success: true });
  } else if (request.action === "getStatus") {
    sendResponse({
      isPlaying: currentSpeechState.isPlaying,
      currentTabId: currentSpeechState.currentTabId,
      requestId: currentSpeechState.requestId,
    });
  }
  return true; // Keep message channel open for async response
});

// Generate unique request ID
function generateRequestId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Stop current speech and reset state
function stopCurrentSpeech(reason = "manual") {
  console.log(`Stopping current speech: ${reason}`);

  // Stop TTS engine
  chrome.tts.stop();

  // Store previous state for notification
  const previousTabId = currentSpeechState.currentTabId;
  const previousRequestId = currentSpeechState.requestId;

  // Clear any pending timeout
  if (currentSpeechState.timeoutId) {
    clearTimeout(currentSpeechState.timeoutId);
  }

  // Reset state
  currentSpeechState = {
    isPlaying: false,
    currentTabId: null,
    currentText: "",
    currentRepeatCount: 0,
    totalRepeats: 1,
    requestId: null,
    startTime: null,
    timeoutId: null,
  };

  // Clear any pending queue processing
  isProcessingQueue = false;

  // Notify content script if there's an active tab
  if (previousTabId) {
    chrome.tabs
      .sendMessage(previousTabId, {
        action: "speechComplete",
        reason: reason,
        requestId: previousRequestId,
      })
      .catch(() => {
        // Ignore errors if tab is closed or content script not available
      });
  }

  // Notify all other tabs that speech has stopped
  notifyAllTabsOfSpeechStop(previousTabId);
}

// Notify all tabs (except the current one) that speech has stopped
function notifyAllTabsOfSpeechStop(excludeTabId) {
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach((tab) => {
      if (tab.id !== excludeTabId) {
        chrome.tabs
          .sendMessage(tab.id, {
            action: "speechStoppedGlobally",
          })
          .catch(() => {
            // Ignore errors for tabs without content script
          });
      }
    });
  });
}

// Main TTS handling function with queue management
async function handleTTSRequest(text, tabId, requestId) {
  try {
    // Validate input
    if (!text || text.trim().length === 0) {
      console.warn("Empty text provided for TTS");
      return;
    }

    if (text.length > 5000) {
      console.warn("Text too long for TTS, truncating to 5000 characters");
      text = text.substring(0, 5000);
    }

    // Check if there's already speech playing
    if (currentSpeechState.isPlaying) {
      console.log(
        `Interrupting current speech (tab ${currentSpeechState.currentTabId}) for new request (tab ${tabId})`
      );
      stopCurrentSpeech("interrupted");
    }

    // Get user settings
    const settings = await chrome.storage.sync.get(["language", "repeatCount"]);
    const language = settings.language || "zh-CN";
    const repeatCount = Math.max(1, Math.min(10, settings.repeatCount || 1));

    // Update state with new request
    currentSpeechState = {
      isPlaying: true,
      currentTabId: tabId,
      currentText: text,
      currentRepeatCount: 0,
      totalRepeats: repeatCount,
      requestId: requestId,
      startTime: Date.now(),
    };

    console.log(
      `Starting TTS for tab ${tabId}, request ${requestId}: "${text.substring(
        0,
        50
      )}..."`
    );

    // Get best voice for the language
    const voice = await getBestVoiceForLanguage(language);

    // Speak the text with repeat functionality
    speakWithRepeat(text, language, repeatCount, tabId, voice, requestId);
  } catch (error) {
    console.error("Error handling TTS request:", error);

    // Reset state on error
    currentSpeechState.isPlaying = false;

    // Notify content script of error
    chrome.tabs
      .sendMessage(tabId, {
        action: "speechError",
        error: error.message,
        requestId: requestId,
      })
      .catch(() => {});
  }
}

// Get the best available voice for a given language
async function getBestVoiceForLanguage(language) {
  return new Promise((resolve) => {
    try {
      // Get voices directly from TTS API for real-time availability
      chrome.tts.getVoices((voices) => {
        console.log(
          `TTS voices check: Found ${voices.length} voices for language ${language}`
        );

        if (voices.length === 0) {
          console.warn("No TTS voices available, but will try system default");
          resolve(null);
          return;
        }

        // Find voices that match the language
        const matchingVoices = voices.filter(
          (voice) => voice.lang && voice.lang.startsWith(language.split("-")[0])
        );

        console.log(
          `Found ${matchingVoices.length} voices matching language ${language}`
        );

        if (matchingVoices.length > 0) {
          // Prefer local voices over remote ones
          const localVoices = matchingVoices.filter((voice) => !voice.remote);
          if (localVoices.length > 0) {
            console.log(
              `Using local voice: ${localVoices[0].voiceName} for ${language}`
            );
            resolve(localVoices[0]);
            return;
          }
          console.log(
            `Using remote voice: ${matchingVoices[0].voiceName} for ${language}`
          );
          resolve(matchingVoices[0]);
          return;
        }

        console.log(
          `No specific voice found for ${language}, using system default`
        );
        resolve(null);
      });
    } catch (error) {
      console.error("Error getting voice for language:", error);
      resolve(null);
    }
  });
}

// Function to handle repeated speech with queue management
function speakWithRepeat(
  text,
  language,
  repeatCount,
  tabId,
  voice,
  requestId,
  currentCount = 0
) {
  // Check if this request is still the current one (not interrupted)
  if (
    !currentSpeechState.isPlaying ||
    currentSpeechState.requestId !== requestId
  ) {
    console.log(`Speech request ${requestId} was interrupted or stopped`);
    return;
  }

  if (currentCount >= repeatCount) {
    // Speech complete, reset state
    console.log(
      `Speech request ${requestId} completed after ${currentCount} repeats`
    );

    currentSpeechState.isPlaying = false;
    currentSpeechState.currentRepeatCount = 0;

    const completionTime = Date.now() - currentSpeechState.startTime;
    console.log(`Total speech duration: ${completionTime}ms`);

    // Notify content script that speech is complete
    chrome.tabs
      .sendMessage(tabId, {
        action: "speechComplete",
        requestId: requestId,
        duration: completionTime,
      })
      .catch(() => {});
    return;
  }

  // Update current repeat count
  currentSpeechState.currentRepeatCount = currentCount;

  // Build TTS options
  const ttsOptions = {
    lang: language,
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    onEvent: (event) => {
      console.log(`TTS Event: ${event.type} for request ${requestId}`);

      if (event.type === "end") {
        // Speech ended, continue with next repeat if needed
        if (
          currentSpeechState.isPlaying &&
          currentSpeechState.requestId === requestId
        ) {
          setTimeout(() => {
            speakWithRepeat(
              text,
              language,
              repeatCount,
              tabId,
              voice,
              requestId,
              currentCount + 1
            );
          }, 300); // Small pause between repeats
        }
      } else if (event.type === "error") {
        console.error("TTS Error:", event.errorMessage || "Unknown TTS error");
        currentSpeechState.isPlaying = false;
        chrome.tabs
          .sendMessage(tabId, {
            action: "speechError",
            error:
              event.errorMessage ||
              "TTS engine error - please check system voice settings",
            requestId: requestId,
          })
          .catch(() => {});
      } else if (event.type === "start") {
        console.log(
          `TTS started for request ${requestId}, repeat ${
            currentCount + 1
          }/${repeatCount}`
        );

        // Clear timeout since TTS started successfully
        if (currentSpeechState.timeoutId) {
          clearTimeout(currentSpeechState.timeoutId);
          currentSpeechState.timeoutId = null;
        }

        // Notify content script that speech started (only on first repeat)
        if (currentCount === 0) {
          chrome.tabs
            .sendMessage(tabId, {
              action: "speechStart",
              requestId: requestId,
            })
            .catch(() => {});
        }
      } else if (event.type === "interrupted") {
        // Speech was interrupted
        console.log(`TTS interrupted for request ${requestId}`);
        currentSpeechState.isPlaying = false;
        chrome.tabs
          .sendMessage(tabId, {
            action: "speechComplete",
            requestId: requestId,
            reason: "interrupted",
          })
          .catch(() => {});
      }
    },
  };

  // Add voice if available
  if (voice && voice.voiceName) {
    ttsOptions.voiceName = voice.voiceName;
    console.log(`Using voice: ${voice.voiceName} for language: ${language}`);
  } else {
    console.log(
      `No specific voice found for language: ${language}, using system default`
    );
  }

  // Log TTS request details
  console.log(
    `TTS Request ${requestId}: speaking "${text.substring(
      0,
      50
    )}..." with options:`,
    ttsOptions
  );

  // Speak the text directly - Chrome will handle voice selection
  try {
    console.log(`Starting TTS for request ${requestId}`);
    console.log("TTS options:", JSON.stringify(ttsOptions, null, 2));

    // Add a timeout to detect if TTS never starts
    const ttsTimeout = setTimeout(() => {
      if (
        currentSpeechState.isPlaying &&
        currentSpeechState.requestId === requestId
      ) {
        console.error(
          `TTS timeout for request ${requestId} - no start event received`
        );
        currentSpeechState.isPlaying = false;
        chrome.tabs
          .sendMessage(tabId, {
            action: "speechError",
            error: "TTS timeout - speech may not be supported on this system",
            requestId: requestId,
          })
          .catch(() => {});
      }
    }, 5000); // 5 second timeout

    // Store timeout ID for cleanup
    currentSpeechState.timeoutId = ttsTimeout;

    chrome.tts.speak(text, ttsOptions);
    console.log(`TTS speak command sent for request ${requestId}`);
  } catch (error) {
    console.error("Error calling chrome.tts.speak:", error);
    currentSpeechState.isPlaying = false;
    chrome.tabs
      .sendMessage(tabId, {
        action: "speechError",
        error: "Failed to start TTS: " + error.message,
        requestId: requestId,
      })
      .catch(() => {});
  }
}
