// Background script for Read Aloud Pal Chrome Extension
// Handles TTS functionality and context menu creation

// Global state management
let currentSpeechState = {
  isPlaying: false,
  currentTabId: null,
  currentText: "",
  currentRepeatCount: 0,
  totalRepeats: 1,
};

// Initialize extension when installed
chrome.runtime.onInstalled.addListener(() => {
  console.log("Read Aloud Pal extension installed");

  // Set default settings
  chrome.storage.sync.set({
    language: "zh-CN", // Default to Chinese
    repeatCount: 1,
    showFloatingButton: true,
  });

  // Create context menu item
  createContextMenu();

  // Initialize TTS voices
  initializeTTSVoices();
});

// Create context menu with proper error handling
function createContextMenu() {
  try {
    chrome.contextMenus.create(
      {
        id: "readAloudPal",
        title: "使用朗讀小夥伴唸出 (Read with Read Aloud Pal)",
        contexts: ["selection"],
      },
      () => {
        if (chrome.runtime.lastError) {
          console.error(
            "Error creating context menu:",
            chrome.runtime.lastError
          );
        } else {
          console.log("Context menu created successfully");
        }
      }
    );
  } catch (error) {
    console.error("Failed to create context menu:", error);
  }
}

// Initialize and cache available TTS voices
function initializeTTSVoices() {
  chrome.tts.getVoices((voices) => {
    console.log("Available TTS voices:", voices.length);
    // Cache voices for better performance
    chrome.storage.local.set({ availableVoices: voices });
  });
}

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "readAloudPal") {
    handleContextMenuClick(info, tab);
  }
});

// Handle context menu click with validation
function handleContextMenuClick(info, tab) {
  try {
    // Validate selection text
    if (!info.selectionText || info.selectionText.trim().length === 0) {
      console.warn("No text selected for context menu TTS");
      return;
    }

    // Validate tab
    if (!tab || !tab.id) {
      console.error("Invalid tab for context menu TTS");
      return;
    }

    // Log context menu usage
    console.log(
      "Context menu TTS triggered for text:",
      info.selectionText.substring(0, 50) + "..."
    );

    // Handle the TTS request
    handleTTSRequest(info.selectionText, tab.id);
  } catch (error) {
    console.error("Error handling context menu click:", error);
  }
}

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "speak") {
    handleTTSRequest(request.text, sender.tab.id);
    sendResponse({ success: true });
  } else if (request.action === "stop") {
    stopCurrentSpeech();
    sendResponse({ success: true });
  }
  return true; // Keep message channel open for async response
});

// Stop current speech and reset state
function stopCurrentSpeech() {
  chrome.tts.stop();
  currentSpeechState.isPlaying = false;
  currentSpeechState.currentRepeatCount = 0;

  // Notify content script if there's an active tab
  if (currentSpeechState.currentTabId) {
    chrome.tabs
      .sendMessage(currentSpeechState.currentTabId, {
        action: "speechComplete",
      })
      .catch(() => {
        // Ignore errors if tab is closed or content script not available
      });
  }
}

// Main TTS handling function
async function handleTTSRequest(text, tabId) {
  try {
    // Validate input
    if (!text || text.trim().length === 0) {
      console.warn("Empty text provided for TTS");
      return;
    }

    if (text.length > 5000) {
      console.warn("Text too long for TTS, truncating to 5000 characters");
      text = text.substring(0, 5000);
    }

    // Get user settings
    const settings = await chrome.storage.sync.get(["language", "repeatCount"]);
    const language = settings.language || "zh-CN";
    const repeatCount = Math.max(1, Math.min(10, settings.repeatCount || 1));

    // Stop any current speech
    stopCurrentSpeech();

    // Update state
    currentSpeechState = {
      isPlaying: true,
      currentTabId: tabId,
      currentText: text,
      currentRepeatCount: 0,
      totalRepeats: repeatCount,
    };

    // Get best voice for the language
    const voice = await getBestVoiceForLanguage(language);

    // Speak the text with repeat functionality
    speakWithRepeat(text, language, repeatCount, tabId, voice);
  } catch (error) {
    console.error("Error handling TTS request:", error);
    // Notify content script of error
    chrome.tabs
      .sendMessage(tabId, {
        action: "speechError",
        error: error.message,
      })
      .catch(() => {});
  }
}

// Get the best available voice for a given language
async function getBestVoiceForLanguage(language) {
  try {
    const storage = await chrome.storage.local.get(["availableVoices"]);
    const voices = storage.availableVoices || [];

    // Find voices that match the language
    const matchingVoices = voices.filter(
      (voice) => voice.lang && voice.lang.startsWith(language.split("-")[0])
    );

    if (matchingVoices.length > 0) {
      // Prefer local voices over remote ones
      const localVoices = matchingVoices.filter((voice) => !voice.remote);
      if (localVoices.length > 0) {
        return localVoices[0];
      }
      return matchingVoices[0];
    }

    return null;
  } catch (error) {
    console.error("Error getting voice for language:", error);
    return null;
  }
}

// Function to handle repeated speech
function speakWithRepeat(
  text,
  language,
  repeatCount,
  tabId,
  voice,
  currentCount = 0
) {
  // Check if speech was stopped
  if (!currentSpeechState.isPlaying) {
    return;
  }

  if (currentCount >= repeatCount) {
    // Speech complete, reset state
    currentSpeechState.isPlaying = false;
    currentSpeechState.currentRepeatCount = 0;

    // Notify content script that speech is complete
    chrome.tabs
      .sendMessage(tabId, { action: "speechComplete" })
      .catch(() => {});
    return;
  }

  // Update current repeat count
  currentSpeechState.currentRepeatCount = currentCount;

  // Build TTS options
  const ttsOptions = {
    lang: language,
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    onEvent: (event) => {
      if (event.type === "end") {
        // Speech ended, continue with next repeat if needed
        if (currentSpeechState.isPlaying) {
          setTimeout(() => {
            speakWithRepeat(
              text,
              language,
              repeatCount,
              tabId,
              voice,
              currentCount + 1
            );
          }, 300); // Small pause between repeats
        }
      } else if (event.type === "error") {
        console.error("TTS Error:", event.errorMessage);
        currentSpeechState.isPlaying = false;
        chrome.tabs
          .sendMessage(tabId, {
            action: "speechError",
            error: event.errorMessage || "TTS playback failed",
          })
          .catch(() => {});
      } else if (event.type === "start") {
        // Notify content script that speech started (only on first repeat)
        if (currentCount === 0) {
          chrome.tabs
            .sendMessage(tabId, { action: "speechStart" })
            .catch(() => {});
        }
      } else if (event.type === "interrupted") {
        // Speech was interrupted
        currentSpeechState.isPlaying = false;
        chrome.tabs
          .sendMessage(tabId, { action: "speechComplete" })
          .catch(() => {});
      }
    },
  };

  // Add voice if available
  if (voice && voice.voiceName) {
    ttsOptions.voiceName = voice.voiceName;
  }

  // Speak the text
  chrome.tts.speak(text, ttsOptions);
}
