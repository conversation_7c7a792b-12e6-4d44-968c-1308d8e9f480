// Content script for Read Aloud Pal Chrome Extension
// Handles text selection detection and floating button display

class ReadAloudPal {
  constructor() {
    this.floatingButton = null;
    this.selectedText = "";
    this.isPlaying = false;
    this.isLoading = false;
    this.showFloatingButton = true;
    this.currentSelection = null;
    this.currentRequestId = null;

    this.init();
  }

  async init() {
    try {
      // Get user settings with error handling
      const settings = await chrome.storage.sync
        .get(["showFloatingButton"])
        .catch(() => ({}));
      this.showFloatingButton = settings.showFloatingButton !== false;

      // Add event listeners with error handling
      document.addEventListener("mouseup", this.handleTextSelection.bind(this));
      document.addEventListener(
        "mousedown",
        this.hideFloatingButton.bind(this)
      );

      // Add error handler for unhandled errors
      window.addEventListener("error", this.handleGlobalError.bind(this));

      // Listen for messages from background script
      chrome.runtime.onMessage.addListener(
        this.handleBackgroundMessage.bind(this)
      );

      console.log("Read Aloud Pal content script initialized");
    } catch (error) {
      console.error("Error initializing Read Aloud Pal:", error);
    }
  }

  // Handle global errors
  handleGlobalError(event) {
    if (
      event.error &&
      event.error.message &&
      event.error.message.includes("Read Aloud Pal")
    ) {
      console.error("Read Aloud Pal error:", event.error);
      // Optionally show user-friendly error
      this.showTemporaryFeedback("Extension error occurred");
    }
  }

  handleTextSelection(event) {
    // Small delay to ensure selection is complete
    setTimeout(() => {
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();

      // Only show button for meaningful text selections (at least 1 character, max 5000 characters)
      if (
        selectedText &&
        selectedText.length >= 1 &&
        selectedText.length <= 5000 &&
        this.showFloatingButton
      ) {
        this.selectedText = selectedText;
        this.displayFloatingButton(selection);
      } else {
        this.hideFloatingButton();
      }
    }, 10);
  }

  displayFloatingButton(selection) {
    // Remove existing button
    this.hideFloatingButton();

    // Validate selection
    if (!selection.rangeCount) return;

    // Get selection position
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    // Skip if selection is not visible
    if (rect.width === 0 && rect.height === 0) return;

    // Create floating button
    this.floatingButton = document.createElement("div");
    this.floatingButton.className = "read-aloud-pal-button";
    this.floatingButton.innerHTML = this.isPlaying ? "⏹️" : "▶️";
    this.floatingButton.title = this.isPlaying ? "Stop reading" : "Read aloud";

    // Calculate optimal position
    let left = rect.right + 5;
    let top = rect.top - 5;

    // Ensure button stays within viewport
    const buttonSize = 24;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (left + buttonSize > viewportWidth) {
      left = rect.left - buttonSize - 5; // Position to the left of selection
    }

    if (top < 0) {
      top = rect.bottom + 5; // Position below selection
    } else if (top + buttonSize > viewportHeight) {
      top = rect.top - buttonSize - 5; // Position above selection
    }

    // Position the button
    this.floatingButton.style.position = "fixed";
    this.floatingButton.style.left = `${left}px`;
    this.floatingButton.style.top = `${top}px`;
    this.floatingButton.style.zIndex = "2147483647"; // Maximum z-index

    // Add click event
    this.floatingButton.addEventListener(
      "click",
      this.handleButtonClick.bind(this)
    );

    // Add to page
    document.body.appendChild(this.floatingButton);
  }

  hideFloatingButton() {
    if (this.floatingButton) {
      this.floatingButton.remove();
      this.floatingButton = null;
    }
  }

  handleButtonClick(event) {
    event.preventDefault();
    event.stopPropagation();

    // Prevent multiple clicks during loading
    if (this.isLoading) {
      return;
    }

    if (this.isPlaying) {
      // Stop current speech
      this.setLoadingState(true);
      chrome.runtime.sendMessage({ action: "stop" }, (response) => {
        if (chrome.runtime.lastError) {
          console.error("Error stopping speech:", chrome.runtime.lastError);
          this.setLoadingState(false);
        }
      });
    } else {
      // Start speech
      if (this.selectedText && this.selectedText.trim().length > 0) {
        this.setLoadingState(true);
        chrome.runtime.sendMessage(
          {
            action: "speak",
            text: this.selectedText,
          },
          (response) => {
            if (chrome.runtime.lastError) {
              console.error("Error starting speech:", chrome.runtime.lastError);
              this.setLoadingState(false);
            }
          }
        );
      } else {
        // No text selected, show feedback
        this.showTemporaryFeedback("No text selected");
      }
    }
  }

  // Set loading state with visual feedback
  setLoadingState(loading) {
    this.isLoading = loading;
    this.updateButtonState();
  }

  // Show temporary feedback to user
  showTemporaryFeedback(message) {
    if (this.floatingButton) {
      const originalContent = this.floatingButton.innerHTML;
      const originalTitle = this.floatingButton.title;

      this.floatingButton.innerHTML = "⚠️";
      this.floatingButton.title = message;
      this.floatingButton.style.backgroundColor = "rgba(255, 193, 7, 0.9)";

      setTimeout(() => {
        if (this.floatingButton) {
          this.floatingButton.innerHTML = originalContent;
          this.floatingButton.title = originalTitle;
          this.floatingButton.style.backgroundColor = "";
        }
      }, 1500);
    }
  }

  handleBackgroundMessage(message, sender, sendResponse) {
    switch (message.action) {
      case "speechStart":
        // Only update if this is for our current request or we don't have one
        if (
          !this.currentRequestId ||
          message.requestId === this.currentRequestId
        ) {
          this.isPlaying = true;
          this.isLoading = false;
          this.currentRequestId = message.requestId;
          this.updateButtonState();
        }
        break;

      case "speechComplete":
        // Only update if this is for our current request
        if (message.requestId === this.currentRequestId) {
          this.isPlaying = false;
          this.isLoading = false;
          this.currentRequestId = null;
          this.updateButtonState();

          if (message.reason === "interrupted") {
            this.showTemporaryFeedback("Speech interrupted by new request");
          }
        }
        break;

      case "speechError":
        // Only update if this is for our current request
        if (message.requestId === this.currentRequestId) {
          this.isPlaying = false;
          this.isLoading = false;
          this.currentRequestId = null;
          this.updateButtonState();
          console.error("Speech error:", message.error);
          this.showTemporaryFeedback(
            "Speech error: " + (message.error || "Unknown error")
          );
        }
        break;

      case "speechStoppedGlobally":
        // Another tab started speech, update our state
        if (this.isPlaying) {
          this.isPlaying = false;
          this.isLoading = false;
          this.currentRequestId = null;
          this.updateButtonState();
          this.showTemporaryFeedback("Speech started in another tab");
        }
        break;

      case "settingsChanged":
        // Update settings when changed from popup
        this.showFloatingButton = message.settings.showFloatingButton !== false;
        if (!this.showFloatingButton) {
          this.hideFloatingButton();
        }
        break;
    }
  }

  updateButtonState() {
    if (this.floatingButton) {
      // Remove all state attributes
      this.floatingButton.removeAttribute("data-loading");
      this.floatingButton.removeAttribute("data-playing");

      if (this.isLoading) {
        this.floatingButton.innerHTML = "⏳";
        this.floatingButton.title = "Loading...";
        this.floatingButton.setAttribute("data-loading", "true");
        this.floatingButton.style.backgroundColor = "rgba(108, 117, 125, 0.9)"; // Gray for loading
      } else if (this.isPlaying) {
        this.floatingButton.innerHTML = "⏹️";
        this.floatingButton.title = "Stop reading (Click to stop)";
        this.floatingButton.setAttribute("data-playing", "true");
        this.floatingButton.style.backgroundColor = "rgba(220, 53, 69, 0.9)"; // Red for stop
      } else {
        this.floatingButton.innerHTML = "▶️";
        this.floatingButton.title = "Read aloud (Click to play)";
        this.floatingButton.style.backgroundColor = "rgba(74, 144, 226, 0.9)"; // Blue for play
      }
    }
  }
}

// Initialize the extension
new ReadAloudPal();
