// Content script for Read Aloud Pal Chrome Extension
// Handles text selection detection and floating button display

class ReadAloudPal {
  constructor() {
    this.floatingButton = null;
    this.selectedText = "";
    this.isPlaying = false;
    this.showFloatingButton = true;

    this.init();
  }

  async init() {
    // Get user settings
    const settings = await chrome.storage.sync.get(["showFloatingButton"]);
    this.showFloatingButton = settings.showFloatingButton !== false;

    // Add event listeners
    document.addEventListener("mouseup", this.handleTextSelection.bind(this));
    document.addEventListener("mousedown", this.hideFloatingButton.bind(this));

    // Listen for messages from background script
    chrome.runtime.onMessage.addListener(
      this.handleBackgroundMessage.bind(this)
    );

    console.log("Read Aloud Pal content script initialized");
  }

  handleTextSelection(event) {
    // Small delay to ensure selection is complete
    setTimeout(() => {
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();

      // Only show button for meaningful text selections (at least 1 character, max 5000 characters)
      if (
        selectedText &&
        selectedText.length >= 1 &&
        selectedText.length <= 5000 &&
        this.showFloatingButton
      ) {
        this.selectedText = selectedText;
        this.displayFloatingButton(selection);
      } else {
        this.hideFloatingButton();
      }
    }, 10);
  }

  displayFloatingButton(selection) {
    // Remove existing button
    this.hideFloatingButton();

    // Validate selection
    if (!selection.rangeCount) return;

    // Get selection position
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    // Skip if selection is not visible
    if (rect.width === 0 && rect.height === 0) return;

    // Create floating button
    this.floatingButton = document.createElement("div");
    this.floatingButton.className = "read-aloud-pal-button";
    this.floatingButton.innerHTML = this.isPlaying ? "⏹️" : "▶️";
    this.floatingButton.title = this.isPlaying ? "Stop reading" : "Read aloud";

    // Calculate optimal position
    let left = rect.right + 5;
    let top = rect.top - 5;

    // Ensure button stays within viewport
    const buttonSize = 24;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (left + buttonSize > viewportWidth) {
      left = rect.left - buttonSize - 5; // Position to the left of selection
    }

    if (top < 0) {
      top = rect.bottom + 5; // Position below selection
    } else if (top + buttonSize > viewportHeight) {
      top = rect.top - buttonSize - 5; // Position above selection
    }

    // Position the button
    this.floatingButton.style.position = "fixed";
    this.floatingButton.style.left = `${left}px`;
    this.floatingButton.style.top = `${top}px`;
    this.floatingButton.style.zIndex = "2147483647"; // Maximum z-index

    // Add click event
    this.floatingButton.addEventListener(
      "click",
      this.handleButtonClick.bind(this)
    );

    // Add to page
    document.body.appendChild(this.floatingButton);
  }

  hideFloatingButton() {
    if (this.floatingButton) {
      this.floatingButton.remove();
      this.floatingButton = null;
    }
  }

  handleButtonClick(event) {
    event.preventDefault();
    event.stopPropagation();

    if (this.isPlaying) {
      // Stop current speech
      chrome.runtime.sendMessage({ action: "stop" });
    } else {
      // Start speech
      if (this.selectedText) {
        chrome.runtime.sendMessage({
          action: "speak",
          text: this.selectedText,
        });
      }
    }
  }

  handleBackgroundMessage(message, sender, sendResponse) {
    switch (message.action) {
      case "speechStart":
        this.isPlaying = true;
        this.updateButtonState();
        break;

      case "speechComplete":
      case "speechError":
        this.isPlaying = false;
        this.updateButtonState();
        if (message.action === "speechError") {
          console.error("Speech error:", message.error);
        }
        break;

      case "settingsChanged":
        // Update settings when changed from popup
        this.showFloatingButton = message.settings.showFloatingButton !== false;
        if (!this.showFloatingButton) {
          this.hideFloatingButton();
        }
        break;
    }
  }

  updateButtonState() {
    if (this.floatingButton) {
      this.floatingButton.innerHTML = this.isPlaying ? "⏹️" : "▶️";
      this.floatingButton.title = this.isPlaying
        ? "Stop reading"
        : "Read aloud";
    }
  }
}

// Initialize the extension
new ReadAloudPal();
